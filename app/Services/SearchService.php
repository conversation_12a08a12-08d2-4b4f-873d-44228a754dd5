<?php

namespace App\Services;

use App\Events\SearchPerformed;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SearchService
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Perform a comprehensive search across parts.
     */
    public function searchParts(Request $request, User $user): array
    {
        $query = $request->get('q', '');
        $searchType = $request->get('type', 'all'); // all, category, model, part_name
        $filters = $this->extractFilters($request);

        // Check if user can search
        if (!$this->subscriptionService->canUserSearch($user)) {
            return [
                'error' => 'Daily search limit exceeded',
                'remaining_searches' => 0,
            ];
        }

        $partsQuery = Part::query()
            ->with(['category', 'models.brand'])
            ->active();

        // Apply search based on type
        switch ($searchType) {
            case 'category':
                $partsQuery = $this->searchByCategory($partsQuery, $query);
                break;
            case 'model':
                $partsQuery = $this->searchByModel($partsQuery, $query);
                break;
            case 'part_name':
                $partsQuery = $this->searchByPartName($partsQuery, $query);
                break;
            default:
                $partsQuery = $this->searchAll($partsQuery, $query);
        }

        // Apply filters
        $partsQuery = $this->applyFilters($partsQuery, $filters);

        // Get results
        $results = $partsQuery->paginate(20);

        // Record the search
        $this->subscriptionService->recordSearch(
            $user,
            $query,
            $searchType,
            $results->total()
        );

        // Log search activity
        $this->logSearchActivity($user, $query, $searchType, $results->total(), $filters);

        // Dispatch search event for plugins
        SearchPerformed::dispatch($user, $query, $searchType, $results->total(), $filters);

        return [
            'results' => $results,
            'filters' => $this->getAvailableFilters(),
            'applied_filters' => $filters,
            'search_type' => $searchType,
            'query' => $query,
            'remaining_searches' => $this->subscriptionService->getRemainingSearches($user),
        ];
    }

    /**
     * Search by category name.
     */
    private function searchByCategory(Builder $query, string $searchTerm): Builder
    {
        return $query->whereHas('category', function ($q) use ($searchTerm) {
            $q->where('name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('description', 'LIKE', "%{$searchTerm}%");
        });
    }

    /**
     * Search by mobile model.
     */
    private function searchByModel(Builder $query, string $searchTerm): Builder
    {
        return $query->whereHas('models', function ($q) use ($searchTerm) {
            $q->where('name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('model_number', 'LIKE', "%{$searchTerm}%")
              ->orWhereHas('brand', function ($brandQuery) use ($searchTerm) {
                  $brandQuery->where('name', 'LIKE', "%{$searchTerm}%");
              });
        });
    }

    /**
     * Search by part name.
     */
    private function searchByPartName(Builder $query, string $searchTerm): Builder
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('part_number', 'LIKE', "%{$searchTerm}%")
              ->orWhere('manufacturer', 'LIKE', "%{$searchTerm}%")
              ->orWhere('description', 'LIKE', "%{$searchTerm}%");
        });
    }

    /**
     * Search across all fields.
     */
    private function searchAll(Builder $query, string $searchTerm): Builder
    {
        if (empty($searchTerm)) {
            return $query;
        }

        return $query->where(function ($q) use ($searchTerm) {
            // Search in parts
            $q->where('name', 'LIKE', "%{$searchTerm}%")
              ->orWhere('part_number', 'LIKE', "%{$searchTerm}%")
              ->orWhere('manufacturer', 'LIKE', "%{$searchTerm}%")
              ->orWhere('description', 'LIKE', "%{$searchTerm}%")
              
              // Search in categories
              ->orWhereHas('category', function ($categoryQuery) use ($searchTerm) {
                  $categoryQuery->where('name', 'LIKE', "%{$searchTerm}%")
                               ->orWhere('description', 'LIKE', "%{$searchTerm}%");
              })
              
              // Search in models and brands
              ->orWhereHas('models', function ($modelQuery) use ($searchTerm) {
                  $modelQuery->where('name', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('model_number', 'LIKE', "%{$searchTerm}%")
                            ->orWhereHas('brand', function ($brandQuery) use ($searchTerm) {
                                $brandQuery->where('name', 'LIKE', "%{$searchTerm}%");
                            });
              });
        });
    }

    /**
     * Apply filters to the query.
     */
    private function applyFilters(Builder $query, array $filters): Builder
    {
        if (!empty($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (!empty($filters['brand_id'])) {
            $query->whereHas('models.brand', function ($q) use ($filters) {
                $q->where('id', $filters['brand_id']);
            });
        }

        if (!empty($filters['manufacturer'])) {
            $query->where('manufacturer', 'LIKE', "%{$filters['manufacturer']}%");
        }

        if (!empty($filters['release_year'])) {
            $query->whereHas('models', function ($q) use ($filters) {
                $q->where('release_year', $filters['release_year']);
            });
        }

        if (!empty($filters['verified_only'])) {
            $query->whereHas('models', function ($q) {
                $q->wherePivot('is_verified', true);
            });
        }

        return $query;
    }

    /**
     * Extract filters from request.
     */
    private function extractFilters(Request $request): array
    {
        return [
            'category_id' => $request->get('category_id'),
            'brand_id' => $request->get('brand_id'),
            'manufacturer' => $request->get('manufacturer'),
            'release_year' => $request->get('release_year'),
            'verified_only' => $request->boolean('verified_only'),
        ];
    }

    /**
     * Get available filter options.
     */
    public function getAvailableFilters(): array
    {
        return [
            'categories' => Category::active()->orderBy('name')->get(['id', 'name']),
            'brands' => Brand::active()->orderBy('name')->get(['id', 'name']),
            'manufacturers' => Part::select('manufacturer')
                ->whereNotNull('manufacturer')
                ->where('manufacturer', '!=', '')
                ->distinct()
                ->orderBy('manufacturer')
                ->pluck('manufacturer'),
            'release_years' => MobileModel::select('release_year')
                ->whereNotNull('release_year')
                ->where('release_year', '!=', '')
                ->distinct()
                ->orderByDesc('release_year')
                ->pluck('release_year'),
        ];
    }

    /**
     * Get search suggestions based on partial query.
     */
    public function getSuggestions(string $query, int $limit = 10, ?int $categoryId = null, ?int $brandId = null): array
    {
        // Part name suggestions
        $partQuery = Part::where('name', 'LIKE', "%{$query}%")
            ->active();

        // Filter by category if provided
        if ($categoryId) {
            $partQuery->where('category_id', $categoryId);
        }

        // Filter by brand if provided
        if ($brandId) {
            $partQuery->whereHas('models.brand', function ($q) use ($brandId) {
                $q->where('id', $brandId);
            });
        }

        $partSuggestions = $partQuery->limit($limit)
            ->pluck('name')
            ->map(fn($name) => ['type' => 'part', 'value' => $name])
            ->toArray();

        // Brand suggestions (only if not filtering by specific brand)
        $brandSuggestions = [];
        if (!$brandId) {
            $brandQuery = Brand::where('name', 'LIKE', "%{$query}%")
                ->active();

            // If filtering by category, only show brands that have parts in that category
            if ($categoryId) {
                $brandQuery->whereHas('models.parts', function ($q) use ($categoryId) {
                    $q->where('category_id', $categoryId);
                });
            }

            $brandSuggestions = $brandQuery->limit($limit)
                ->pluck('name')
                ->map(fn($name) => ['type' => 'brand', 'value' => $name])
                ->toArray();
        }

        // Model suggestions
        $modelQuery = MobileModel::where('name', 'LIKE', "%{$query}%")
            ->active()
            ->with('brand');

        // Filter models by category if provided
        if ($categoryId) {
            $modelQuery->whereHas('parts', function ($q) use ($categoryId) {
                $q->where('category_id', $categoryId);
            });
        }

        // Filter models by brand if provided
        if ($brandId) {
            $modelQuery->where('brand_id', $brandId);
        }

        $modelSuggestions = $modelQuery->limit($limit)
            ->get()
            ->map(fn($model) => [
                'type' => 'model',
                'value' => $model->name,
                'brand' => $model->brand->name
            ])
            ->toArray();

        // Category suggestions (only if not filtering by specific category)
        $categorySuggestions = [];
        if (!$categoryId) {
            $categorySuggestions = Category::where('name', 'LIKE', "%{$query}%")
                ->active()
                ->limit($limit)
                ->pluck('name')
                ->map(fn($name) => ['type' => 'category', 'value' => $name])
                ->toArray();
        }

        return array_merge($partSuggestions, $brandSuggestions, $modelSuggestions, $categorySuggestions);
    }

    /**
     * Get related parts for a specific part.
     */
    public function getRelatedParts(Part $part, int $limit = 5): array
    {
        // Get parts from the same category
        $relatedByCategory = Part::where('category_id', $part->category_id)
            ->where('id', '!=', $part->id)
            ->active()
            ->with(['category', 'models.brand'])
            ->limit($limit)
            ->get();

        // Get parts compatible with the same models
        $modelIds = $part->models->pluck('id');
        $relatedByModels = Part::whereHas('models', function ($q) use ($modelIds) {
                $q->whereIn('models.id', $modelIds);
            })
            ->where('id', '!=', $part->id)
            ->active()
            ->with(['category', 'models.brand'])
            ->limit($limit)
            ->get();

        return [
            'by_category' => $relatedByCategory,
            'by_models' => $relatedByModels,
        ];
    }

    /**
     * Log search activity to UserActivityLog.
     */
    private function logSearchActivity(User $user, string $query, string $searchType, int $resultsCount, array $filters): void
    {
        try {
            UserActivityLog::create([
                'user_id' => $user->id,
                'activity_type' => 'search',
                'description' => "User performed search: '{$query}'",
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => [
                    'search_query' => $query,
                    'search_type' => $searchType,
                    'results_count' => $resultsCount,
                    'filters_applied' => $filters,
                    'timestamp' => now()->toISOString(),
                ],
                'performed_by' => null, // User performed this themselves
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the search
            Log::error('Failed to log search activity', [
                'user_id' => $user->id,
                'query' => $query,
                'error' => $e->getMessage()
            ]);
        }
    }
}
